from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from .models import Role, Permission, UserRole, RolePermission

User = get_user_model()


class AuthenticationModelTests(TestCase):
    """认证模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            real_name='Test User'
        )
        self.role = Role.objects.create(
            name='测试角色',
            code='test',
            description='测试角色描述'
        )
        self.permission = Permission.objects.create(
            name='测试权限',
            code='TEST_001',
            description='测试权限描述',
            resource='test',
            action='read'
        )

    def test_user_creation(self):
        """测试用户创建"""
        self.assertEqual(self.user.username, 'testuser')
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.real_name, 'Test User')
        self.assertTrue(self.user.check_password('testpass123'))

    def test_role_creation(self):
        """测试角色创建"""
        self.assertEqual(self.role.name, '测试角色')
        self.assertEqual(self.role.code, 'test')
        self.assertTrue(self.role.is_active)

    def test_permission_creation(self):
        """测试权限创建"""
        self.assertEqual(self.permission.name, '测试权限')
        self.assertEqual(self.permission.code, 'TEST_001')
        self.assertEqual(self.permission.resource, 'test')
        self.assertEqual(self.permission.action, 'read')

    def test_user_role_assignment(self):
        """测试用户角色分配"""
        user_role = UserRole.objects.create(user=self.user, role=self.role)
        self.assertEqual(user_role.user, self.user)
        self.assertEqual(user_role.role, self.role)

    def test_role_permission_assignment(self):
        """测试角色权限分配"""
        role_permission = RolePermission.objects.create(role=self.role, permission=self.permission)
        self.assertEqual(role_permission.role, self.role)
        self.assertEqual(role_permission.permission, self.permission)


class AuthenticationAPITests(APITestCase):
    """认证API测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            real_name='Test User'
        )
        self.role = Role.objects.create(
            name='测试角色',
            code='test',
            description='测试角色描述'
        )
        self.permission = Permission.objects.create(
            name='测试权限',
            code='TEST_001',
            description='测试权限描述',
            resource='test',
            action='read'
        )
        UserRole.objects.create(user=self.user, role=self.role)
        RolePermission.objects.create(role=self.role, permission=self.permission)

    def test_login_success(self):
        """测试登录成功"""
        url = reverse('token_obtain_pair')
        data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertIn('access', response_data['data'])
        self.assertIn('refresh', response_data['data'])

    def test_login_failure(self):
        """测试登录失败"""
        url = reverse('token_obtain_pair')
        data = {
            'username': 'testuser',
            'password': 'wrongpassword'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_user_info_authenticated(self):
        """测试获取用户信息（已认证）"""
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        url = reverse('user_info')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['data']['username'], 'testuser')
        self.assertEqual(response_data['data']['realName'], 'Test User')
        self.assertIn('test', response_data['data']['roles'])

    def test_user_info_unauthenticated(self):
        """测试获取用户信息（未认证）"""
        url = reverse('user_info')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_access_codes_authenticated(self):
        """测试获取权限码（已认证）"""
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        url = reverse('access_codes')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertIn('TEST_001', response_data['data'])

    def test_logout_authenticated(self):
        """测试登出（已认证）"""
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        url = reverse('logout')
        data = {'refresh': str(refresh)}
        response = self.client.post(url, data, format='json')
        # 登出可能返回200或400（如果token已经失效）
        self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST])
        response_data = response.json()
        # 检查响应包含消息或错误信息
        self.assertTrue('message' in response_data['data'] or 'error' in response_data['data'])
