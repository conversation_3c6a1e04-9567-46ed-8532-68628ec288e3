import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {
      // 自定义应用配置
    },
    vite: {
      server: {
        proxy: {
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ''),
            // 自定义API代理目标地址
            target: 'http://localhost:3000/api',
            ws: true,
          },
        },
      },
    },
  };
});
