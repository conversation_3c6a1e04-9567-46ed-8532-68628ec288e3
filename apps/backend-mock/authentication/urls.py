from django.urls import path
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)
from . import views

urlpatterns = [
    # 带斜杠的URL（原有的）
    path('login/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('me/', views.UserInfoView.as_view(), name='user_info'),
    path('codes/', views.AccessCodesView.as_view(), name='access_codes'),

    # 不带斜杠的URL（前端兼容）
    path('login', TokenObtainPairView.as_view(), name='token_obtain_pair_no_slash'),
    path('token/refresh', TokenRefreshView.as_view(), name='token_refresh_no_slash'),
    path('logout', views.LogoutView.as_view(), name='logout_no_slash'),
    path('me', views.UserInfoView.as_view(), name='user_info_no_slash'),
    path('codes', views.AccessCodesView.as_view(), name='access_codes_no_slash'),
]
