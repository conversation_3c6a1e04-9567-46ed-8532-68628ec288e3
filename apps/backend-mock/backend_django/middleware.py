import json
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin


class ResponseFormatMiddleware(MiddlewareMixin):
    """
    响应格式化中间件
    将Django REST framework的响应格式化为前端期望的格式
    """
    
    def process_response(self, request, response):
        # 只处理API请求
        if not request.path.startswith('/api/'):
            return response
        
        # 只处理JSON响应
        if not isinstance(response, JsonResponse) and not (
            hasattr(response, 'content_type') and 
            'application/json' in response.get('Content-Type', '')
        ):
            return response
        
        # 跳过某些特殊端点
        skip_paths = [
            '/api/status/',  # 状态检查端点保持原格式
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return response
        
        try:
            # 解析响应内容
            if hasattr(response, 'data'):
                # DRF Response对象
                data = response.data
            else:
                # 普通JsonResponse
                data = json.loads(response.content.decode('utf-8'))
            
            # 检查是否已经是包装格式
            if isinstance(data, dict) and 'data' in data:
                return response
            
            # 包装响应数据
            wrapped_data = {
                'data': data,
                'status': 'success' if 200 <= response.status_code < 300 else 'error',
                'code': 0 if 200 <= response.status_code < 300 else response.status_code
            }
            
            # 创建新的响应
            new_response = JsonResponse(wrapped_data, status=response.status_code)
            
            # 复制原响应的头部
            for header, value in response.items():
                if header.lower() not in ['content-length', 'content-type']:
                    new_response[header] = value
            
            return new_response
            
        except (json.JSONDecodeError, AttributeError):
            # 如果解析失败，返回原响应
            return response
