from rest_framework import status, generics, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from .models import User, Role, Permission, UserRole, RolePermission
from .serializers import (
    UserSerializer, UserCreateSerializer, RoleSerializer, PermissionSerializer,
    LoginSerializer, ChangePasswordSerializer
)

User = get_user_model()


class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get("refresh")
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            return Response({"message": "Successfully logged out"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class UserInfoView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data)


class AccessCodesView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        # 获取用户的所有权限代码
        user_roles = UserRole.objects.filter(user=user)
        permission_codes = []

        for user_role in user_roles:
            role_permissions = RolePermission.objects.filter(role=user_role.role)
            for rp in role_permissions:
                if rp.permission.code not in permission_codes:
                    permission_codes.append(rp.permission.code)

        return Response(permission_codes)
