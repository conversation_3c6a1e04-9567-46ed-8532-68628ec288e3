import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';

import { getAccessCodesApi, getUserInfoApi, loginApi, logoutApi } from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   * @param onSuccess 成功之后的回调函数
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      console.log('开始登录，参数:', params);

      const response = await loginApi(params);
      console.log('登录 API 响应:', response);

      // Django JWT 返回 access 和 refresh token
      const accessToken = response.access || response.accessToken;
      console.log('获取到 accessToken:', accessToken);

      // 如果成功获取到 accessToken
      if (accessToken) {
        accessStore.setAccessToken(accessToken);
        console.log('accessToken 已设置');

        // 获取用户信息并存储到 accessStore 中
        const [fetchUserInfoResult, accessCodes] = await Promise.all([
          fetchUserInfo(),
          getAccessCodesApi(),
        ]);

        userInfo = fetchUserInfoResult;
        console.log('获取到用户信息:', userInfo);

        userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(accessCodes);
        console.log('用户信息和权限码已设置');

        if (accessStore.loginExpired) {
          console.log('登录已过期，重置状态');
          accessStore.setLoginExpired(false);
        } else {
          const targetPath = userInfo.homePath || preferences.app.defaultHomePath;
          console.log('登录成功，准备跳转到:', targetPath);
          console.log('用户信息:', userInfo);
          console.log('默认首页路径:', preferences.app.defaultHomePath);
          console.log('当前路由:', router.currentRoute.value);

          if (onSuccess) {
            console.log('执行 onSuccess 回调');
            await onSuccess?.();
          } else {
            console.log('执行路由跳转到:', targetPath);
            try {
              const result = await router.push(targetPath);
              console.log('路由跳转结果:', result);
            } catch (error) {
              console.error('路由跳转失败:', error);
            }
          }
        }

        if (userInfo?.realName) {
          notification.success({
            description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3,
            message: $t('authentication.loginSuccess'),
          });
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }

    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    userInfo = await getUserInfoApi();
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
